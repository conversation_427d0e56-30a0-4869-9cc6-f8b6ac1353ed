import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams, useNavigate } from "react-router-dom";
import SellerLayout from "../../components/seller/SellerLayout";
import { FiUpload, FiInfo, FiCheck } from "react-icons/fi";
import { MdPlayArrow, MdDownload, MdVisibility } from "react-icons/md";
import {
  getSellerContentById,
  updateContent,
  uploadContentFile,
} from "../../redux/slices/contentSlice";
import chunkedUploadService from "../../services/chunkedUploadService";
import SummernoteEditor from "../../components/common/SummernoteEditor";
import UploadProgressBar from "../../components/common/UploadProgressBar";
import DocumentViewer from "../../components/common/DocumentViewer";
import PreviewModal from "../../components/common/PreviewModal";
import { showSuccess, showError } from "../../utils/toast";
import {
  getImageUrl,
  getPlaceholderImage,
  getSmartFileUrl,
  getProxyContentUrl,
  getProxyStreamUrl,
  getProxyThumbnailUrl,
  getProxyUrlWithAuth,
  IMAGE_BASE_URL,
} from "../../utils/constants";
import {
  validateFileByContentType,
  getAcceptAttribute,
  isFileUploadDisabled,
  FILE_SIZE_LIMITS
} from "../../utils/fileValidation";
import PreviewStatusIndicator from "../../components/common/PreviewStatusIndicator";
import {
  VALIDATION_LIMITS,
} from "../../utils/textValidation";
import { toUTC, formatForDateTimeLocal } from "../../utils/timezoneUtils";
import TimezoneErrorBoundary from "../../components/common/TimezoneErrorBoundary";
import TimezoneInfo from "../../components/common/TimezoneInfo";

import "../../styles/AddStrategy.css";

const THUMBNAIL_LIMITS = {
  maxSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif']
};

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (bytes >= 1024 * 1024 * 1024) {
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)}GB`;
  } else if (bytes >= 1024 * 1024) {
    return `${(bytes / (1024 * 1024)).toFixed(0)}MB`;
  } else if (bytes >= 1024) {
    return `${(bytes / 1024).toFixed(0)}KB`;
  }
  return `${bytes}B`;
};

const EditStrategy = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { singleContent, isLoading } = useSelector((state) => state.content);

  // State for array field inputs
  const [newTag, setNewTag] = useState("");

  // Form state - Visible fields as per screenshot
  const [formData, setFormData] = useState({
    // Visible fields
    title: "",
    category: "",
    coachName: "",
    description: "",
    fileUrl: "",
    aboutCoach: "",
    strategicContent: "",

    // Hidden fields with default values for backend compatibility
    sport: "Other",
    contentType: "Video",
    previewUrl: "",
    thumbnailUrl: "",
    duration: "",
    videoLength: "",
    fileSize: "",
    tags: [],
    difficulty: "Intermediate",
    language: "English",
    prerequisites: [],
    learningObjectives: [],
    equipment: [],
    saleType: "Fixed",
    price: 0,
    allowCustomRequests: false,
    customRequestPrice: "",
    status: "Draft",
    visibility: "Public",

    // Auction-specific fields
    auctionDetails: {
      basePrice: "",
      auctionStartDate: "",
      auctionEndDate: "",
      minimumBidIncrement: "",
      allowOfferBeforeAuctionStart: false,
    },
  });

  // File upload state
  const [uploadedFile, setUploadedFile] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Upload progress states
  const [showUploadProgress, setShowUploadProgress] = useState(false);
  const [currentUploadType, setCurrentUploadType] = useState("");
  const [currentFileName, setCurrentFileName] = useState("");
  const [uploadProgress, setUploadProgress] = useState(0);

  // Enhanced upload states
  const [uploadStats, setUploadStats] = useState({});
  const [uploadError, setUploadError] = useState(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [canRetry, setCanRetry] = useState(false);
  const [currentUploadId, setCurrentUploadId] = useState(null);
  const [useChunkedUpload, setUseChunkedUpload] = useState(false);

  // Thumbnail validation states
  const [thumbnailError, setThumbnailError] = useState("");
  const [showThumbnailPreview, setShowThumbnailPreview] = useState(true);

  // Validation state
  const [validationErrors, setValidationErrors] = useState({});
  const [showValidation, setShowValidation] = useState(false);

  // Preview modal state
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [formSubmitted, setFormSubmitted] = useState(false);

  // Add state to track if we've attempted to fetch data
  const [hasFetchedData, setHasFetchedData] = useState(false);
  const [fetchError, setFetchError] = useState(null);

  // Fetch content data when component mounts
  useEffect(() => {
    if (id && !hasFetchedData) {
      setHasFetchedData(true);
      setFetchError(null);

      dispatch(getSellerContentById(id))
        .unwrap()
        .then(() => { })
        .catch((error) => {
          console.error("Failed to fetch strategy data:", error); // Debug log
          setFetchError(error.message || "Failed to load strategy data");
          showError("Failed to load strategy data. Please try again.");
        });
    }
  }, [dispatch, id, hasFetchedData]);

  // Initialize form data when single content is loaded
  useEffect(() => {
    if (singleContent) {
      setFormData({
        // Include the ID for proxy URL generation
        _id: singleContent._id,

        // Visible fields
        title: singleContent.title || "",
        category: singleContent.category || "",
        coachName: singleContent.coachName || "",
        description: singleContent.description || "",
        fileUrl: singleContent.fileUrl || "",
        aboutCoach: singleContent.aboutCoach || "",
        strategicContent: singleContent.strategicContent || "",

        // Fields from JSON example
        sport: singleContent.sport || "Basketball",
        contentType: singleContent.contentType || "",
        thumbnailUrl: singleContent.thumbnailUrl || "",
        tags: singleContent.tags || [],
        difficulty: singleContent.difficulty || "",
        saleType: singleContent.saleType || "",
        price: singleContent.price || "",
        allowCustomRequests: singleContent.allowCustomRequests || false,

        // Auction-specific fields - use standardized conversion
        auctionDetails: {
          basePrice: singleContent.auctionDetails?.basePrice || "",
          auctionStartDate: singleContent.auctionDetails?.auctionStartDate
            ? formatForDateTimeLocal(new Date(singleContent.auctionDetails.auctionStartDate))
            : "",
          auctionEndDate: singleContent.auctionDetails?.auctionEndDate
            ? formatForDateTimeLocal(new Date(singleContent.auctionDetails.auctionEndDate))
            : "",
          minimumBidIncrement: singleContent.auctionDetails?.minimumBidIncrement || "",
          allowOfferBeforeAuctionStart: singleContent.auctionDetails?.allowOfferBeforeAuctionStart || false,
        },

        // Other hidden fields with default values for backend compatibility
        previewUrl: singleContent.previewUrl || "",
        duration: singleContent.duration || "",
        videoLength: singleContent.videoLength || "",
        fileSize: singleContent.fileSize || "",
        prerequisites: singleContent.prerequisites || [],
        learningObjectives: singleContent.learningObjectives || [],
        equipment: singleContent.equipment || [],
        customRequestPrice: singleContent.customRequestPrice || "",
        status: singleContent.status || "Published",
        visibility: singleContent.visibility || "Public",
      });
    }
  }, [singleContent]);

  // Handle input changes with validation
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // Special handling for content type changes
    if (name === "contentType" && uploadedFile) {
      // Validate existing file against new content type
      const validation = validateFileByContentType(uploadedFile, value);
      if (!validation.isValid) {
        // Clear the file if it doesn't match the new content type
        setUploadedFile(null);
        setFormData(prev => ({
          ...prev,
          fileUrl: "",
          fileSize: "",
        }));
        showError(`Current file is not valid for ${value} content type. Please upload a new file.`);

        // Clear file input if it exists
        const fileInput = document.querySelector('input[type="file"]');
        if (fileInput) {
          fileInput.value = "";
        }
      }
    }

    // Validate field on change
    validateField(name, type === "checkbox" ? checked : value);
  };

  // Handle Summernote changes
  const handleSummernoteChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Validate field on change
    validateField(field, value);
  };

  // Handle field blur event
  const handleFieldBlur = (e) => {
    const { name, value } = e.target;
    validateField(name, value);
  };

  // Validate individual field
  const validateField = (fieldName, value) => {
    const errors = { ...validationErrors };

    switch (fieldName) {
      case "title":
        if (!value.trim()) {
          errors.title = "Strategy title is required";
        } else {
          delete errors.title;
        }
        break;

      case "category":
        if (!value) {
          errors.category = "Please select a category";
        } else {
          delete errors.category;
        }
        break;

      case "coachName":
        if (!value.trim()) {
          errors.coachName = "Coach/Seller/Academy name is required";
        } else {
          delete errors.coachName;
        }
        break;

      case "description": {
        const cleanDescription = value.replace(/<[^>]*>/g, "").trim();
        if (!cleanDescription) {
          errors.description = "Strategy description is required";
        } else {
          delete errors.description;
        }
        break;
      }

      case "aboutCoach": {
        const cleanAboutCoach = value.replace(/<[^>]*>/g, "").trim();
        if (!cleanAboutCoach) {
          errors.aboutCoach = "About the coach information is required";
        } else {
          delete errors.aboutCoach;
        }
        break;
      }

      case "strategicContent": {
        const cleanStrategicContent = value.replace(/<[^>]*>/g, "").trim();
        if (!cleanStrategicContent) {
          errors.strategicContent = "Strategic content description is required";
        } else {
          delete errors.strategicContent;
        }
        break;
      }

      case "contentType":
        if (!value) {
          errors.contentType = "Please select a content type";
        } else {
          delete errors.contentType;
        }
        break;

      case "difficulty":
        if (!value) {
          errors.difficulty = "Please select a difficulty level";
        } else {
          delete errors.difficulty;
        }
        break;

      case "saleType":
        if (!value) {
          errors.saleType = "Please select a sale type";
        } else {
          delete errors.saleType;
        }
        break;

      case "price":
        if (!value || value <= 0) {
          errors.price = "Please enter a valid price greater than $0";
        } else {
          delete errors.price;
        }
        break;

      default:
        break;
    }

    setValidationErrors(errors);
  };

  // Handle file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // Ensure content type is selected first
      if (!formData.contentType) {
        showError("Please select a content type before uploading a file");
        e.target.value = ""; // Clear the file input
        return;
      }

      // Validate file based on selected content type
      const validation = validateFileByContentType(file, formData.contentType);
      if (!validation.isValid) {
        showError(validation.message);
        e.target.value = ""; // Clear the file input
        return;
      }

      setUploadedFile(file);
      setShowUploadProgress(true);
      setCurrentUploadType("content file");
      setCurrentFileName(file.name);
      setUploadProgress(0);
      setUploadError(null);
      setCanRetry(false);
      setUploadStats({});

      // Determine if we should use chunked upload (for files > 100MB)
      const shouldUseChunkedUpload = file.size > 100 * 1024 * 1024; // 100MB threshold
      setUseChunkedUpload(shouldUseChunkedUpload);

      try {
        let result;

        if (shouldUseChunkedUpload) {
          result = await chunkedUploadService.uploadFile(
            file,
            formData.contentType, // Pass content type
            (stats) => {
              setUploadProgress(stats.progress);
              setUploadStats(stats);
            },
            (error) => {
              setUploadError(error);
              setCanRetry(true);
            },
            (chunkIndex, retryCount, maxRetries) => {
              setIsRetrying(true);
            }
          );
        } else {
          const formDataUpload = new FormData();
          formDataUpload.append("file", file);
          formDataUpload.append("type", "content");

          result = await dispatch(
            uploadContentFile({
              formData: formDataUpload,
              onProgress: (progress) => setUploadProgress(progress)
            })
          ).unwrap();
        }

        setFormData((prev) => ({
          ...prev,
          fileUrl: result.data.fileUrl,
          fileSize: result.data.fileSize || file.size,
        }));

        // Clear file upload validation error
        const errors = { ...validationErrors };
        delete errors.fileUpload;
        setValidationErrors(errors);

        showSuccess("Content file uploaded successfully!");
      } catch (error) {
        console.error("File upload failed:", error);
        setUploadedFile(null);
        setUploadError(error.message || "Failed to upload content file. Please try again.");
        setCanRetry(true);
        showError(error.message || "Failed to upload content file. Please try again.");
      } finally {
        setIsRetrying(false);
        if (!uploadError) {
          // Only hide progress if no error occurred
          setShowUploadProgress(false);
          setCurrentUploadType("");
          setCurrentFileName("");
          setUploadProgress(0);
          setUploadStats({});
        }
      }
    }
  };

  // Handle upload retry
  const handleUploadRetry = async () => {
    if (!currentUploadId) {
      // Restart the upload from beginning
      const fileInput = document.getElementById('file-upload');
      if (fileInput && fileInput.files[0]) {
        handleFileUpload({ target: fileInput });
      }
      return;
    }

    setIsRetrying(true);
    setUploadError(null);

    try {
      const result = await chunkedUploadService.resumeUpload(currentUploadId);

      setFormData((prev) => ({
        ...prev,
        fileUrl: result.data.fileUrl,
        fileSize: result.data.fileSize,
      }));

      // Clear file upload validation error
      const errors = { ...validationErrors };
      delete errors.fileUpload;
      setValidationErrors(errors);

      showSuccess("Content file uploaded successfully!");
      setShowUploadProgress(false);
      setCurrentUploadType("");
      setCurrentFileName("");
      setUploadProgress(0);
      setUploadStats({});
      setCanRetry(false);
    } catch (error) {
      console.error("Upload retry failed:", error);
      setUploadError(error.message || "Retry failed. Please try again.");
      showError(error.message || "Retry failed. Please try again.");
    } finally {
      setIsRetrying(false);
    }
  };

  // Handle upload cancel
  const handleUploadCancel = async () => {
    if (currentUploadId) {
      try {
        await chunkedUploadService.cancelUpload(currentUploadId);
        showSuccess("Upload cancelled and chunks cleaned up.");
      } catch (error) {
        console.error('Error cancelling upload:', error);
        showError("Upload cancelled locally, but server cleanup may have failed.");
      }
    }

    setShowUploadProgress(false);
    setCurrentUploadType("");
    setCurrentFileName("");
    setUploadProgress(0);
    setUploadStats({});
    setUploadError(null);
    setCanRetry(false);
    setCurrentUploadId(null);
    setUploadedFile(null);

    // Clear the file input
    const fileInput = document.getElementById('file-upload');
    if (fileInput) {
      fileInput.value = "";
    }
  };

  // Handle thumbnail upload
  const handleThumbnailUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Clear previous errors
    setThumbnailError("");
    setShowThumbnailPreview(false);

    try {
      // Validate file type
      if (!THUMBNAIL_LIMITS.allowedTypes.includes(file.type)) {
        throw new Error(
          "Only JPG, JPEG, PNG, and GIF formats are supported for thumbnails"
        );
      }

      // Validate file size
      if (file.size > THUMBNAIL_LIMITS.maxSize) {
        throw new Error("Thumbnail file size must be less than 5MB");
      }

      // Show upload progress
      setShowUploadProgress(true);
      setCurrentUploadType("thumbnail");
      setCurrentFileName(file.name);
      setUploadProgress(0); // Reset progress

      const formDataUpload = new FormData();
      formDataUpload.append("file", file);
      formDataUpload.append("type", "thumbnail");

      const response = await dispatch(
        uploadContentFile({
          formData: formDataUpload,
          onProgress: (progress) => setUploadProgress(progress)
        })
      ).unwrap();

      if (!response.data || !response.data.fileUrl) {
        throw new Error("Invalid response from server");
      }

      // Store the complete URL path
      const thumbnailUrl = response.data.fileUrl;
      // Update form data with the new thumbnail URL
      setFormData((prev) => ({
        ...prev,
        thumbnailUrl: thumbnailUrl,
      }));

      setShowThumbnailPreview(true);
      showSuccess("Thumbnail uploaded successfully!");
    } catch (error) {
      console.error("Thumbnail upload failed:", error);
      setThumbnailError(
        error.message || "Failed to upload thumbnail. Please try again."
      );
      setFormData((prev) => ({
        ...prev,
        thumbnailUrl: "",
      }));
    } finally {
      setShowUploadProgress(false);
      setCurrentUploadType("");
      setCurrentFileName("");
      setUploadProgress(0); // Reset progress
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormSubmitted(true);
    setIsSubmitting(true);

    // Validate all fields before submission
    const allErrors = {};

    if (!formData.title.trim()) {
      allErrors.title = "Strategy title is required";
    }

    if (!formData.category) {
      allErrors.category = "Please select a category";
    }

    if (!formData.coachName.trim()) {
      allErrors.coachName = "Coach/Seller/Academy name is required";
    }

    if (!formData.description.replace(/<[^>]*>/g, "").trim()) {
      allErrors.description = "Strategy description is required";
    }

    if (!formData.contentType) {
      allErrors.contentType = "Please select a content type";
    }

    if (!formData.fileUrl && !uploadedFile) {
      allErrors.fileUpload = "Please upload a video or document file";
    }

    if (!formData.aboutCoach.replace(/<[^>]*>/g, "").trim()) {
      allErrors.aboutCoach = "About the coach information is required";
    }

    if (!formData.strategicContent.replace(/<[^>]*>/g, "").trim()) {
      allErrors.strategicContent = "Strategic content description is required";
    }

    if (!formData.thumbnailUrl) {
      allErrors.thumbnailUpload = "Please upload a thumbnail image";
    }

    if (!formData.difficulty) {
      allErrors.difficulty = "Please select a difficulty level";
    }

    if (!formData.saleType) {
      allErrors.saleType = "Please select a sale type";
    }

    // Validate price only for Fixed sale type
    if (formData.saleType === "Fixed") {
      if (!formData.price || formData.price <= 0) {
        allErrors.price = "Please enter a valid price greater than $0";
      }
    }

    // Validate auction details for Auction sale type
    if (formData.saleType === "Auction") {
      if (!formData.auctionDetails.basePrice || formData.auctionDetails.basePrice <= 0) {
        allErrors.auctionBasePrice = "Please enter a valid starting bid price greater than $0";
      }

      if (!formData.auctionDetails.minimumBidIncrement || formData.auctionDetails.minimumBidIncrement <= 0) {
        allErrors.auctionMinIncrement = "Please enter a valid minimum bid increment greater than $0";
      }

      if (!formData.auctionDetails.auctionStartDate) {
        allErrors.auctionStartDate = "Please select an auction start date";
      } else {
        // Validate that start date is in the future
        const startDate = new Date(formData.auctionDetails.auctionStartDate);
        const now = new Date();
        if (startDate <= now) {
          allErrors.auctionStartDate = "Auction start date must be in the future";
        }
      }

      if (!formData.auctionDetails.auctionEndDate) {
        allErrors.auctionEndDate = "Please select an auction end date";
      }

      // Validate that end date is after start date
      if (formData.auctionDetails.auctionStartDate && formData.auctionDetails.auctionEndDate) {
        const startDate = new Date(formData.auctionDetails.auctionStartDate);
        const endDate = new Date(formData.auctionDetails.auctionEndDate);

        if (endDate <= startDate) {
          allErrors.auctionDateRange = "Auction end date must be after start date";
        }
      }
    }

    // If there are validation errors, show them and stop submission
    if (Object.keys(allErrors).length > 0) {
      setValidationErrors(allErrors);
      setShowValidation(true);
      setIsSubmitting(false);

      // Scroll to the first error field
      setTimeout(() => {
        const firstErrorField = document.querySelector(
          ".AddStrategy__validation-error"
        );
        if (firstErrorField) {
          firstErrorField.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
        }
      }, 100);

      return;
    }

    try {
      // Prepare data for submission
      const submitData = {
        ...formData,
        sport: formData.category || "Other",
        coachName: formData.coachName || "Coach",
        thumbnailUrl: formData.thumbnailUrl,
      };

      // Convert auction dates to UTC before submission if it's an auction
      if (submitData.saleType === "Auction") {
        if (submitData.auctionDetails.auctionStartDate) {
          submitData.auctionDetails.auctionStartDate = toUTC(new Date(submitData.auctionDetails.auctionStartDate));
        }
        if (submitData.auctionDetails.auctionEndDate) {
          submitData.auctionDetails.auctionEndDate = toUTC(new Date(submitData.auctionDetails.auctionEndDate));
        }
      }

      await dispatch(updateContent({ id, contentData: submitData })).unwrap();

      showSuccess("🎉 Strategy updated successfully!");
      navigate(`/seller/my-sports-strategies`);
    } catch (error) {
      console.error("Content update failed:", error);
      let errorMessage = "Failed to update strategy. Please try again.";

      if (error.message) {
        errorMessage = error.message;
      } else if (error.errors && error.errors.length > 0) {
        errorMessage = error.errors[0].msg || errorMessage;
      } else if (typeof error === "string") {
        errorMessage = error;
      }

      showError(`❌ ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while fetching content
  if ((isLoading && !singleContent) || (!singleContent && !hasFetchedData)) {
    return (
      <SellerLayout>
        <div className="AddStrategy">
          <div className="loading-container">
            <div className="loading-spinner"></div>
            <p>Loading strategy details...</p>
          </div>
        </div>
      </SellerLayout>
    );
  }

  // Show error state if content not found after fetch attempt
  if (!singleContent && hasFetchedData && !isLoading) {
    return (
      <SellerLayout>
        <div className="AddStrategy">
          <div className="error-container">
            <h3>Strategy not found</h3>
            <p>
              {fetchError ||
                "The strategy you're trying to edit doesn't exist or has been removed."}
            </p>
            <div className="error-actions">
              <button
                className="btn btn-primary"
                onClick={() => {
                  setHasFetchedData(false);
                  setFetchError(null);
                }}
              >
                Try Again
              </button>
              <button
                className="btn btn-outline"
                onClick={() => navigate("/seller/my-sports-strategies")}
              >
                Back to Strategies
              </button>
            </div>
          </div>
        </div>
      </SellerLayout>
    );
  }

  return (
    <SellerLayout>
      <div className="AddStrategy">
        {/* Header */}
        <div className="AddStrategy__header">
          <p className="AddStrategy__subtitle">Update your strategy details</p>
        </div>

        {/* Main Form */}
        <form className="AddStrategy__form" onSubmit={handleSubmit}>
          {/* Strategy Title */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Strategy Title</label>
            <input
              type="text"
              name="title"
              className="AddStrategy__input"
              placeholder="Add title for Strategy"
              value={formData.title}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            />
            {(validationErrors.title ||
              (formSubmitted && !formData.title.trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.title || "Strategy title is required"}
                  </p>
                </div>
              )}
          </div>

          {/* Category */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Category</label>
            <select
              name="category"
              className="AddStrategy__select"
              value={formData.category}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Category</option>
              <option value="Basketball">Basketball</option>
              <option value="Football">Football</option>
              <option value="Soccer">Soccer</option>
              <option value="Baseball">Baseball</option>
            </select>
            {(validationErrors.category ||
              (formSubmitted && !formData.category)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.category || "Please select a category"}
                  </p>
                </div>
              )}
          </div>

          {/* Coach/Seller/Academy Name */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">
              Coach/Seller/Academy Name
            </label>
            <input
              type="text"
              name="coachName"
              className="AddStrategy__input"
              placeholder="Enter coach, seller, or academy name"
              value={formData.coachName}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            />
            {(validationErrors.coachName ||
              (showValidation && !formData.coachName.trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.coachName ||
                      "Coach/Seller/Academy name is required"}
                  </p>
                </div>
              )}
          </div>

          {/* Description for Strategy - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">
              Description for Strategy
            </label>
            <SummernoteEditor
              value={formData.description}
              onChange={(value) => handleSummernoteChange("description", value)}
              placeholder="Enter a detailed description of your strategy..."
              height={200}
              className="AddStrategy__summernote"
              contentKey={`desc-${id}`}
            />
            {(validationErrors.description ||
              (formSubmitted &&
                !formData.description.replace(/<[^>]*>/g, "").trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.description ||
                      "Strategy description is required"}
                  </p>
                </div>
              )}
          </div>

          {/* Content Type */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Content Type</label>
            <select
              name="contentType"
              className="AddStrategy__select"
              value={formData.contentType}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Content Type</option>
              <option value="Video">Video</option>
              <option value="Document">Document</option>
            </select>
            {(validationErrors.contentType ||
              (formSubmitted && !formData.contentType)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.contentType ||
                      "Please select a content type"}
                  </p>
                </div>
              )}
          </div>

          {/* File Upload */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">
              Upload {formData.contentType || "Video/Document"}
            </label>
            {formData.contentType && (
              <p className="AddStrategy__format-note">
                {formData.contentType === 'Video' ? (
                  <>Maximum size: <span>{formatFileSize(FILE_SIZE_LIMITS.Video)}</span> • Supported formats: <span>MP4, MOV, AVI, WEBM</span></>
                ) : formData.contentType === 'Document' ? (
                  <>Maximum size: <span>{formatFileSize(FILE_SIZE_LIMITS.Document)}</span> • Supported formats: <span>PDF</span></>
                ) : (
                  'Please select a content type to see upload requirements'
                )}
              </p>
            )}
            <label
              htmlFor="file-upload"
              className="AddStrategy__upload"
            >
              <input
                type="file"
                id="file-upload"
                className="AddStrategy__file-input"
                onChange={handleFileUpload}
                accept={getAcceptAttribute(formData.contentType)}
                disabled={isFileUploadDisabled(formData.contentType)}
                style={{ display: "none" }}
              />
              <div
                className={`AddStrategy__upload-content ${isFileUploadDisabled(formData.contentType)
                  ? "AddStrategy__upload-content--disabled"
                  : ""
                  }`}
              >
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  {uploadedFile
                    ? uploadedFile.name
                    : formData.fileUrl
                      ? "Current file uploaded"
                      : "Click to upload file"}
                </p>
              </div>

              {/* File info display */}
              {(uploadedFile || formData.fileUrl) && (
                <div className="AddStrategy__file-info">
                  <p className="AddStrategy__file-name">
                    {uploadedFile ? uploadedFile.name : "Current file uploaded"}
                  </p>
                  {uploadedFile && (
                    <p className="AddStrategy__file-size">
                      {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  )}
                </div>
              )}
            </label>



            {/* Validation error message */}
            {(validationErrors.fileUpload ||
              (formSubmitted && !formData.fileUrl && !uploadedFile)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.fileUpload ||
                      "Please upload a video or document file"}
                  </p>
                </div>
              )}

            {/* File Preview Section */}
            {formData.fileUrl && (
              <div className="AddStrategy__file-preview">
                <div className="AddStrategy__preview-header">
                  <h4 className="AddStrategy__preview-title">
                    Current File Preview
                  </h4>
                  <button
                    className="btn-outline AddStrategy__previewBtn"
                    onClick={() => setIsPreviewModalOpen(true)}
                    title="Preview Document/Video in Full Screen"
                    type="button"
                  >
                    <MdVisibility />
                    Preview
                  </button>
                </div>
                {formData.contentType === "Video" && (
                  <div className="AddStrategy__video-preview">
                    <video
                      className="AddStrategy__video-element"
                      controls
                      controlsList="nodownload nofullscreen noremoteplayback"
                      disablePictureInPicture
                      style={{ width: "100%", maxHeight: "300px" }}
                      onError={(e) => {
                        console.error("Video preview error:", e);
                      }}
                    >
                      <source
                        src={formData._id
                          ? getProxyUrlWithAuth(getProxyStreamUrl(formData._id))
                          : getImageUrl(formData.fileUrl)
                        }
                        type="video/mp4"
                      />
                      Your browser does not support the video tag.
                    </video>
                  </div>
                )}
                {formData.contentType === "Document" && (
                  <div className="AddStrategy__document-preview">
                    <DocumentViewer
                      fileUrl={formData._id
                        ? getProxyUrlWithAuth(getProxyContentUrl(formData._id))
                        : formData.fileUrl
                      }
                      fileName={
                        uploadedFile?.name ||
                        formData.fileUrl?.split("/").pop() ||
                        "document"
                      }
                      title="Document Preview"
                      className="AddStrategy__document-element"
                      height="300px"
                      showDownload={false}
                    />
                  </div>
                )}
              </div>
            )}
          </div>

          {/* About The Coach - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">About The Coach</label>
            <SummernoteEditor
              value={formData.aboutCoach}
              onChange={(value) => handleSummernoteChange("aboutCoach", value)}
              placeholder="Share your background, experience, and expertise..."
              height={200}
              className="AddStrategy__summernote"
              contentKey={`coach-${id}`}
            />
            {(validationErrors.aboutCoach ||
              (formSubmitted &&
                !formData.aboutCoach.replace(/<[^>]*>/g, "").trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.aboutCoach ||
                      "About the coach information is required"}
                  </p>
                </div>
              )}
          </div>

          {/* Includes Strategic Content - Rich Text Editor */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">
              Includes Strategic Content
            </label>
            <SummernoteEditor
              value={formData.strategicContent}
              onChange={(value) =>
                handleSummernoteChange("strategicContent", value)
              }
              placeholder="Describe what strategic content is included..."
              height={200}
              className="AddStrategy__summernote"
              contentKey={`strategic-${id}`}
            />
            {(validationErrors.strategicContent ||
              (formSubmitted &&
                !formData.strategicContent.replace(/<[^>]*>/g, "").trim())) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.strategicContent ||
                      "Strategic content description is required"}
                  </p>
                </div>
              )}
          </div>

          {/* Thumbnail Upload */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Thumbnail Image</label>
            <p className="AddStrategy__format-note">
              Maximum size: <span>5MB</span> • Supported formats: <span>JPG, JPEG, PNG, GIF</span>
            </p>
            <label
              htmlFor="thumbnail-upload"
              className="AddStrategy__upload"
            >
              <input
                type="file"
                id="thumbnail-upload"
                className="AddStrategy__file-input"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                onChange={handleThumbnailUpload}
                style={{ display: "none" }}
              />
              <div className="AddStrategy__upload-content">
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  {formData.thumbnailUrl
                    ? "Thumbnail uploaded"
                    : "Click to upload thumbnail"}
                </p>
              </div>

              {/* Thumbnail validation error */}
              {thumbnailError && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">{thumbnailError}</p>
                </div>
              )}

              {/* Required field validation error */}
              {(validationErrors.thumbnailUpload ||
                (formSubmitted && !formData.thumbnailUrl)) && (
                  <div className="AddStrategy__validation-error">
                    <p className="AddStrategy__error-message">
                      {validationErrors.thumbnailUpload ||
                        "Please upload a thumbnail image"}
                    </p>
                  </div>
                )}

              {/* Thumbnail preview */}
              {formData.thumbnailUrl && showThumbnailPreview && (
                <div className="AddStrategy__thumbnail-preview">
                  <img
                    src={formData._id
                      ? getProxyUrlWithAuth(getProxyThumbnailUrl(formData._id))
                      : getImageUrl(formData.thumbnailUrl)
                    }
                    alt="Thumbnail preview"
                    onError={(e) => {
                      e.target.src = getPlaceholderImage();
                      showError("Failed to load thumbnail preview");
                    }}
                    style={{
                      maxWidth: "100%",
                      height: "auto",
                      borderRadius: "var(--border-radius)",
                    }}
                  />
                </div>
              )}

              {/* Show placeholder when no thumbnail URL */}
              {!formData.thumbnailUrl && (
                <div className="AddStrategy__thumbnail-preview">
                  <img
                    src={getPlaceholderImage(200, 120, "No thumbnail")}
                    alt="No thumbnail"
                    style={{
                      maxWidth: "100%",
                      height: "auto",
                      borderRadius: "var(--border-radius)",
                      opacity: 0.7,
                    }}
                  />
                </div>
              )}
            </label>
          </div>

          {/* Difficulty Level */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Difficulty Level</label>
            <select
              name="difficulty"
              className="AddStrategy__select"
              value={formData.difficulty}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Difficulty</option>
              <option value="Beginner">Beginner</option>
              <option value="Intermediate">Intermediate</option>
              <option value="Advanced">Advanced</option>
              <option value="Professional">Professional</option>
            </select>
            {(validationErrors.difficulty ||
              (formSubmitted && !formData.difficulty)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.difficulty ||
                      "Please select a difficulty level"}
                  </p>
                </div>
              )}
          </div>

          {/* Language */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Language</label>
            <select
              name="language"
              className="AddStrategy__select"
              value={formData.language}
              onChange={handleInputChange}
            >
              <option value="">Select Language</option>
              <option value="English">English</option>
              <option value="Spanish">Spanish</option>
              <option value="French">French</option>
              <option value="German">German</option>
              <option value="Chinese">Chinese</option>
              <option value="Japanese">Japanese</option>
              <option value="Other">Other</option>
            </select>
          </div>

          {/* Tags */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Tags</label>
            <div className="AddStrategy__array-field">
              <div className="AddStrategy__array-input">
                <input
                  type="text"
                  className="AddStrategy__input"
                  placeholder="Add a tag (e.g., basketball, technique, training)..."
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      if (newTag.trim()) {
                        setFormData((prev) => ({
                          ...prev,
                          tags: [...prev.tags, newTag.trim()],
                        }));
                        setNewTag("");
                      }
                    }
                  }}
                />
                <button
                  type="button"
                  className="btn-primary"
                  onClick={() => {
                    if (newTag.trim()) {
                      setFormData((prev) => ({
                        ...prev,
                        tags: [...prev.tags, newTag.trim()],
                      }));
                      setNewTag("");
                    }
                  }}
                >
                  Add
                </button>
              </div>
              {formData.tags.length > 0 && (
                <div className="AddStrategy__array-items">
                  {formData.tags.map((tag, index) => (
                    <div key={index} className="AddStrategy__array-item">
                      {tag}
                      <button
                        type="button"
                        className="AddStrategy__remove-btn"
                        onClick={() => {
                          setFormData((prev) => ({
                            ...prev,
                            tags: prev.tags.filter((_, i) => i !== index),
                          }));
                        }}
                      >
                        X
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Sale Type */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Sale Type</label>
            <select
              name="saleType"
              className="AddStrategy__select"
              value={formData.saleType}
              onChange={handleInputChange}
              onBlur={handleFieldBlur}
            >
              <option value="">Select Sale Type</option>
              <option value="Fixed">Fixed Price</option>
              <option value="Auction">Auction</option>
            </select>
            {(validationErrors.saleType ||
              (formSubmitted && !formData.saleType)) && (
                <div className="AddStrategy__validation-error">
                  <p className="AddStrategy__error-message">
                    {validationErrors.saleType || "Please select a sale type"}
                  </p>
                </div>
              )}
          </div>

          {/* Price - Only show for Fixed sale type */}
          {formData.saleType === "Fixed" && (
            <div className="AddStrategy__field">
              <label className="AddStrategy__label">Price ($)</label>
              <input
                type="number"
                name="price"
                className="AddStrategy__input"
                placeholder="Enter price"
                value={formData.price}
                onChange={handleInputChange}
                onBlur={handleFieldBlur}
                min="0"
                step="0.01"
              />
              {(validationErrors.price ||
                (formSubmitted && (!formData.price || formData.price <= 0))) && (
                  <div className="AddStrategy__validation-error">
                    <p className="AddStrategy__error-message">
                      {validationErrors.price ||
                        "Please enter a valid price greater than $0"}
                    </p>
                  </div>
                )}
            </div>
          )}

          {/* Allow Custom Requests */}
          {/* <div className="AddStrategy__field">
            <label className="AddStrategy__checkbox-label">
              <input
                type="checkbox"
                name="allowCustomRequests"
                checked={formData.allowCustomRequests}
                onChange={handleInputChange}
                className="AddStrategy__checkbox"
              />
              Allow Custom Requests
            </label>
          </div> */}

          {/* Custom Request Price (shown only if allowCustomRequests is true) */}
          {formData.allowCustomRequests && (
            <div className="AddStrategy__field">
              <label className="AddStrategy__label">
                Custom Request Price ($)
              </label>
              <input
                type="number"
                name="customRequestPrice"
                className="AddStrategy__input"
                placeholder="Enter custom request price"
                value={formData.customRequestPrice}
                onChange={handleInputChange}
                min="0"
                step="0.01"
              />
            </div>
          )}

          {/* Auction Fields - Show only when Auction is selected */}
          {formData.saleType === "Auction" && (
            <div className="AddStrategy__auction-section">
              <h3 className="AddStrategy__section-title">Auction Settings</h3>

              {/* Base Price */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__label">
                  Starting Bid Price ($)
                </label>
                <input
                  type="number"
                  name="auctionDetails.basePrice"
                  className="AddStrategy__input"
                  placeholder="Enter starting bid price"
                  value={formData.auctionDetails.basePrice}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      auctionDetails: {
                        ...prev.auctionDetails,
                        basePrice: e.target.value,
                      },
                    }))
                  }
                  min="0"
                  step="0.01"
                />
                {(validationErrors.auctionBasePrice ||
                  (formSubmitted && (!formData.auctionDetails.basePrice || formData.auctionDetails.basePrice <= 0))) && (
                    <div className="AddStrategy__validation-error">
                      <p className="AddStrategy__error-message">
                        {validationErrors.auctionBasePrice || "Please enter a valid starting bid price greater than $0"}
                      </p>
                    </div>
                  )}
              </div>

              {/* Minimum Bid Increment */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__label">
                  Minimum Bid Increment ($)
                </label>
                <input
                  type="number"
                  name="auctionDetails.minimumBidIncrement"
                  className="AddStrategy__input"
                  placeholder="Enter minimum bid increment"
                  value={formData.auctionDetails.minimumBidIncrement}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      auctionDetails: {
                        ...prev.auctionDetails,
                        minimumBidIncrement: e.target.value,
                      },
                    }))
                  }
                  min="0.01"
                  step="0.01"
                />
                {(validationErrors.auctionMinIncrement ||
                  (formSubmitted && (!formData.auctionDetails.minimumBidIncrement || formData.auctionDetails.minimumBidIncrement <= 0))) && (
                    <div className="AddStrategy__validation-error">
                      <p className="AddStrategy__error-message">
                        {validationErrors.auctionMinIncrement || "Please enter a valid minimum bid increment greater than $0"}
                      </p>
                    </div>
                  )}
              </div>

              {/* Auction Start Date */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__label">Auction Start Date</label>
                <TimezoneErrorBoundary>
                  <input
                    type="datetime-local"
                    name="auctionDetails.auctionStartDate"
                    className="AddStrategy__input"
                    value={formData.auctionDetails.auctionStartDate}
                    min={formatForDateTimeLocal(new Date())}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFormData((prev) => ({
                        ...prev,
                        auctionDetails: {
                          ...prev.auctionDetails,
                          auctionStartDate: value,
                        },
                      }));

                      // Real-time validation using UTC comparison
                      if (value) {
                        const startDate = toUTC(new Date(value));
                        const now = new Date();
                        if (startDate <= now) {
                          setValidationErrors(prev => ({
                            ...prev,
                            auctionStartDate: "Auction start date must be in the future"
                          }));
                        } else {
                          setValidationErrors(prev => {
                            const newErrors = { ...prev };
                            delete newErrors.auctionStartDate;
                            return newErrors;
                          });
                        }
                      }
                    }}
                  />
                  <TimezoneInfo />
                </TimezoneErrorBoundary>
                {(validationErrors.auctionStartDate ||
                  (formSubmitted && !formData.auctionDetails.auctionStartDate)) && (
                    <div className="AddStrategy__validation-error">
                      <p className="AddStrategy__error-message">
                        {validationErrors.auctionStartDate || "Please select an auction start date"}
                      </p>
                    </div>
                  )}
              </div>

              {/* Auction End Date */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__label">Auction End Date</label>
                <TimezoneErrorBoundary>
                  <input
                    type="datetime-local"
                    name="auctionDetails.auctionEndDate"
                    className="AddStrategy__input"
                    value={formData.auctionDetails.auctionEndDate}
                    min={formatForDateTimeLocal(formData.auctionDetails.auctionStartDate ? new Date(formData.auctionDetails.auctionStartDate) : new Date())}
                    onChange={(e) => {
                      const value = e.target.value;
                      setFormData((prev) => ({
                        ...prev,
                        auctionDetails: {
                          ...prev.auctionDetails,
                          auctionEndDate: value,
                        },
                      }));

                      // Real-time validation using UTC comparison
                      if (value && formData.auctionDetails.auctionStartDate) {
                        const startDate = toUTC(new Date(formData.auctionDetails.auctionStartDate));
                        const endDate = toUTC(new Date(value));
                        if (endDate <= startDate) {
                          setValidationErrors(prev => ({
                            ...prev,
                            auctionDateRange: "Auction end date must be after start date"
                          }));
                        } else {
                          setValidationErrors(prev => {
                            const newErrors = { ...prev };
                            delete newErrors.auctionDateRange;
                            return newErrors;
                          });
                        }
                      }
                    }}
                  />
                  <TimezoneInfo />
                </TimezoneErrorBoundary>
                {(validationErrors.auctionEndDate || validationErrors.auctionDateRange ||
                  (formSubmitted && !formData.auctionDetails.auctionEndDate)) && (
                    <div className="AddStrategy__validation-error">
                      <p className="AddStrategy__error-message">
                        {validationErrors.auctionEndDate || validationErrors.auctionDateRange || "Please select an auction end date"}
                      </p>
                    </div>
                  )}
              </div>

              {/* Allow Offers Before Auction Start */}
              <div className="AddStrategy__field">
                <label className="AddStrategy__checkbox-label">
                  <input
                    type="checkbox"
                    name="auctionDetails.allowOfferBeforeAuctionStart"
                    checked={
                      formData.auctionDetails.allowOfferBeforeAuctionStart
                    }
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        auctionDetails: {
                          ...prev.auctionDetails,
                          allowOfferBeforeAuctionStart: e.target.checked,
                        },
                      }))
                    }
                    className="AddStrategy__checkbox"
                  />
                  Allow offers before auction starts
                </label>
              </div>

              {/* Auction Note */}

              <div className="AddStrategy__field">
                <div className="AddStrategy__auction-note">
                  <p>
                    <strong>Note:</strong>
                  </p>
                  <p>
                    (1) Once the auction starts, the strategy content cannot be edited.
                  </p>
                  <p>
                    (2) If no bids are manually accepted or rejected, the highest bidder automatically wins the auction at its conclusion
                  </p>
                  <p>
                    and receives a payment link.
                  </p>
                  <p>
                    (3) If no bids are placed, the content is offered to all users at the base price as a fixed-price sale once the auction ends.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="AddStrategy__actions">
            <button
              type="submit"
              className="btn-primary"
              disabled={isSubmitting || isLoading}
            >
              {isSubmitting ? "Updating..." : "Update Strategy"}
            </button>
            <button
              type="button"
              className=" btn-outline"
              onClick={() => navigate(`/seller/strategy-details/${id}`)}
              disabled={isSubmitting || isLoading}
            >
              Cancel
            </button>
          </div>
        </form>

        {/* Upload Progress Bar */}
        <UploadProgressBar
          isVisible={showUploadProgress}
          progress={uploadProgress}
          fileName={currentFileName}
          uploadType={currentUploadType}
          uploadStats={uploadStats}
          error={uploadError}
          isRetrying={isRetrying}
          canRetry={canRetry}
          canCancel={true}
          onRetry={handleUploadRetry}
          onCancel={handleUploadCancel}
        />

        {/* Preview Modal */}
        <PreviewModal
          isOpen={isPreviewModalOpen}
          onClose={() => setIsPreviewModalOpen(false)}
          fileUrl={formData._id
            ? getProxyUrlWithAuth(
              formData.contentType === "Video"
                ? getProxyStreamUrl(formData._id)
                : getProxyContentUrl(formData._id)
            )
            : getSmartFileUrl(formData.fileUrl)
          }
          fileName={
            uploadedFile?.name ||
            formData.fileUrl?.split("/").pop() ||
            formData.title ||
            "document"
          }
          title={formData.title || "Strategy Preview"}
          contentType={formData.contentType}
        />
      </div>
    </SellerLayout>
  );
};

export default EditStrategy;
