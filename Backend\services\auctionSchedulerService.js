const dynamicAuctionScheduler = require('../jobs/dynamicAuctionScheduler');

class AuctionSchedulerService {
    /**
     * Schedule auction resolution when auction is created
     * @param {Object} auction - The auction content object
     */
    static scheduleAuctionResolution(auction) {
        try {
            if (!auction || !auction._id || !auction.auctionDetails?.auctionEndDate) {
                console.log('⚠️ Invalid auction data for scheduling');
                return;
            }

            // Only schedule for auction type content
            if (auction.saleType !== 'Auction' && auction.saleType !== 'Both') {
                return;
            }

            // Only schedule if auction is active and not sold
            if (auction.auctionStatus === 'Ended' || auction.isSold) {
                return;
            }

            const auctionData = {
                title: auction.title,
                seller: auction.seller,
                sport: auction.sport,
                contentType: auction.contentType
            };

            dynamicAuctionScheduler.scheduleAuctionJob(
                auction._id.toString(),
                auction.auctionDetails.auctionEndDate,
                auctionData
            );

            console.log(`📅 Scheduled auction resolution for "${auction.title}" (${auction._id})`);

        } catch (error) {
            console.error('❌ Error scheduling auction resolution:', error);
        }
    }

    /**
     * Update auction schedule when auction is modified
     * @param {Object} auction - The updated auction content object
     */
    static updateAuctionSchedule(auction) {
        try {
            if (!auction || !auction._id) {
                console.log('⚠️ Invalid auction data for schedule update');
                return;
            }

            // Cancel existing schedule first
            this.cancelAuctionSchedule(auction._id.toString());

            // Reschedule if still valid
            this.scheduleAuctionResolution(auction);

            console.log(`🔄 Updated auction schedule for "${auction.title}" (${auction._id})`);

        } catch (error) {
            console.error('❌ Error updating auction schedule:', error);
        }
    }

    /**
     * Cancel auction schedule when auction is ended manually or deleted
     * @param {string} auctionId - The auction ID
     */
    static cancelAuctionSchedule(auctionId) {
        try {
            dynamicAuctionScheduler.cancelAuctionJob(auctionId);
            console.log(`🛑 Cancelled auction schedule for ${auctionId}`);
        } catch (error) {
            console.error('❌ Error cancelling auction schedule:', error);
        }
    }

    /**
     * Schedule all existing active auctions (for system startup)
     */
    static async scheduleExistingAuctions() {
        try {
            const Content = require('../models/Content');
            
            // Find all active auctions that haven't ended
            const activeAuctions = await Content.find({
                saleType: { $in: ['Auction', 'Both'] },
                auctionStatus: { $ne: 'Ended' },
                isSold: false,
                status: 'Published',
                'auctionDetails.auctionEndDate': { $exists: true }
            }).select('_id title seller sport contentType auctionDetails saleType');

            console.log(`📊 Found ${activeAuctions.length} active auctions to schedule`);

            let scheduledCount = 0;
            for (const auction of activeAuctions) {
                // Only schedule future auctions
                const now = new Date();
                if (auction.auctionDetails.auctionEndDate > now) {
                    this.scheduleAuctionResolution(auction);
                    scheduledCount++;
                } else {
                    console.log(`⏰ Auction ${auction._id} has already ended, skipping schedule`);
                }
            }

            console.log(`✅ Scheduled ${scheduledCount} auction resolution jobs`);
            return scheduledCount;

        } catch (error) {
            console.error('❌ Error scheduling existing auctions:', error);
            return 0;
        }
    }

    /**
     * Get status of all scheduled auctions
     */
    static getScheduledAuctionsStatus() {
        return dynamicAuctionScheduler.getStatus();
    }

    /**
     * Clean up expired auction jobs
     */
    static cleanupExpiredJobs() {
        return dynamicAuctionScheduler.cleanupExpiredJobs();
    }
}

module.exports = AuctionSchedulerService;
