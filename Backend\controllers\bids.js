const ErrorResponse = require("../utils/errorResponse");
const Bid = require("../models/Bid");
const Content = require("../models/Content");
const Offer = require("../models/Offer");
const { validationResult } = require("express-validator");
const sendEmail = require("../utils/sendEmail");
const { generateBidAcceptanceEmail, generateBidRejectionEmail } = require("../utils/emailTemplates");
const AuctionSchedulerService = require("../services/auctionSchedulerService");

// @desc    Get all bids
// @route   GET /api/bids
// @access  Private/Admin
exports.getBids = async (req, res, next) => {
  try {
    const bids = await Bid.find()
      .populate({
        path: "bidder",
        select: "firstName lastName email",
      })
      .populate("content");

    res.status(200).json({
      success: true,
      count: bids.length,
      data: bids,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single bid
// @route   GET /api/bids/:id
// @access  Private
exports.getBid = async (req, res, next) => {
  try {
    const bid = await Bid.findById(req.params.id)
      .populate({
        path: "bidder",
        select: "firstName lastName email",
      })
      .populate({
        path: "content",
        populate: {
          path: "seller",
          select: "firstName lastName email",
        },
      });

    if (!bid) {
      return next(
        new ErrorResponse(`Bid not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is bid owner or content seller or admin
    if (
      bid.bidder._id.toString() !== req.user.id &&
      bid.content.seller._id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view this bid`,
          403
        )
      );
    }

    res.status(200).json({
      success: true,
      data: bid,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get highest active bid for content
// @route   GET /api/bids/content/:contentId/highest
// @access  Public
exports.getHighestActiveBid = async (req, res, next) => {
  try {
    const highestBid = await Bid.findOne({
      content: req.params.contentId,
      status: "Active",
    }).sort("-amount");

    res.status(200).json({
      success: true,
      data: highestBid,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create new bid
// @route   POST /api/bids
// @access  Private/Buyer
exports.createBid = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { contentId, amount, isAutoBid, maxAutoBidAmount } = req.body;

    // Check if user is a buyer (use effective role for non-admin users)
    const effectiveRole =
      req.user.role === "admin" ? req.user.role : req.user.activeRole;
    if (effectiveRole !== "buyer" && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to create a bid`,
          403
        )
      );
    }

    // Get content
    const content = await Content.findById(contentId);
    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${contentId}`, 404)
      );
    }

    // Check if content is published
    if (content.status !== "Published") {
      return next(
        new ErrorResponse(`Content is not available for bidding`, 400)
      );
    }

    // Check if content allows auction
    if (content.saleType !== "Auction" && content.saleType !== "Both") {
      return next(new ErrorResponse(`Content does not allow auction`, 400));
    }

    // Check if content is sold
    if (content.isSold) {
      return next(new ErrorResponse(`Content has already been sold`, 400));
    }

    // Check if auction has been ended (by bid acceptance)
    if (content.auctionStatus === "Ended") {
      return next(new ErrorResponse(`Auction has ended`, 400));
    }

    // Check if auction has started
    if (
      content.auctionDetails.auctionStartDate &&
      new Date(content.auctionDetails.auctionStartDate) > new Date()
    ) {
      return next(new ErrorResponse(`Auction has not started yet`, 400));
    }

    // Check if auction has ended by time
    if (
      content.auctionDetails.auctionEndDate &&
      new Date(content.auctionDetails.auctionEndDate) < new Date()
    ) {
      return next(new ErrorResponse(`Auction has ended`, 400));
    }

    // Check if bid amount is greater than base price (starting bid)
    const basePrice =
      content.auctionDetails.basePrice ||
      content.auctionDetails.startingBid ||
      0;
    if (amount < basePrice) {
      return next(
        new ErrorResponse(
          `Bid amount must be greater than starting bid of $${basePrice}`,
          400
        )
      );
    }

    // Get highest active bid
    const highestBid = await Bid.findOne({
      content: contentId,
      status: "Active",
    }).sort("-amount");

    // Check if bid amount is greater than highest bid + minimum increment
    const minIncrement =
      content.auctionDetails.minimumBidIncrement ||
      content.auctionDetails.minIncrement ||
      1;

    if (highestBid && amount < highestBid.amount + minIncrement) {
      return next(
        new ErrorResponse(
          `Bid amount must be at least $${(
            highestBid.amount + minIncrement
          ).toFixed(2)}`,
          400
        )
      );
    }

    // Check if user already has an active bid
    const existingBid = await Bid.findOne({
      content: contentId,
      bidder: req.user.id,
      status: "Active",
    });

    // If user has an existing active bid, mark it as outbid
    if (existingBid) {
      existingBid.status = "Outbid";
      await existingBid.save();
    }

    // Create new bid
    const bid = await Bid.create({
      content: contentId,
      bidder: req.user.id,
      amount,
      isAutoBid,
      maxAutoBidAmount
    });

    // Update all pending offers to Expired when any bid is placed
    await Offer.updateMany(
      { content: contentId, status: "Pending" },
      { status: "Expired" }
    );

    res.status(201).json({
      success: true,
      data: bid,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Cancel bid
// @route   PUT /api/bids/:id/cancel
// @access  Private/Buyer
exports.cancelBid = async (req, res, next) => {
  try {
    const bid = await Bid.findById(req.params.id);

    if (!bid) {
      return next(
        new ErrorResponse(`Bid not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is bid owner
    if (bid.bidder.toString() !== req.user.id && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to cancel this bid`,
          403
        )
      );
    }

    // Check if bid is active
    if (bid.status !== "Active") {
      return next(new ErrorResponse(`Only active bids can be cancelled`, 400));
    }

    // Update bid status to cancelled
    bid.status = "Cancelled";
    await bid.save();

    // Find user's previous highest bid on the same content that was marked as 'Outbid'
    const previousBid = await Bid.findOne({
      content: bid.content,
      bidder: req.user.id,
      status: "Outbid",
      _id: { $ne: bid._id }, // Exclude the current cancelled bid
    }).sort("-amount"); // Get the highest previous bid

    let reactivatedBid = null;

    if (previousBid) {
      // Check if this previous bid would now be the highest active bid
      const currentHighestBid = await Bid.findOne({
        content: bid.content,
        status: "Active",
        _id: { $ne: bid._id }, // Exclude the cancelled bid
      }).sort("-amount");

      // If no other active bids exist, or if the previous bid is higher than current highest
      if (!currentHighestBid || previousBid.amount > currentHighestBid.amount) {
        // Reactivate the previous bid
        previousBid.status = "Active";
        await previousBid.save();
        reactivatedBid = previousBid;

        // If there was a current highest bid, mark it as outbid
        if (currentHighestBid) {
          currentHighestBid.status = "Outbid";
          await currentHighestBid.save();
        }
      }
    }

    res.status(200).json({
      success: true,
      message: reactivatedBid
        ? "Bid cancelled and previous bid reactivated"
        : "Bid cancelled successfully",
      data: {
        cancelledBid: bid,
        reactivatedBid: reactivatedBid,
      },
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get bids for content
// @route   GET /api/bids/content/:contentId
// @access  Public
exports.getContentBids = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.contentId);

    if (!content) {
      return next(
        new ErrorResponse(
          `Content not found with id of ${req.params.contentId}`,
          404
        )
      );
    }

    const bids = await Bid.find({ content: req.params.contentId })
      .populate({
        path: "bidder",
        select: "firstName lastName",
      })
      .sort("-amount");

    res.status(200).json({
      success: true,
      count: bids.length,
      data: bids,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get user bids
// @route   GET /api/bids/user
// @access  Private
exports.getUserBids = async (req, res, next) => {
  try {
    // Get pagination parameters from query
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await Bid.countDocuments({ bidder: req.user.id });

    // Get paginated bids
    const bids = await Bid.find({ bidder: req.user.id })
      .populate({
        path: "content",
        select:
          "title sport contentType price thumbnailUrl auctionDetails seller",
        populate: {
          path: "seller",
          select: "firstName lastName",
        },
      })
      .sort("-createdAt")
      .skip(startIndex)
      .limit(limit);

    // Get order information for each bid to check payment status
    const Order = require("../models/Order");
    const bidsWithOrderInfo = await Promise.all(
      bids.map(async (bid) => {
        const bidObj = bid.toObject();
        if (bid.status === "Won") {
          // Find the order for this bid
          const order = await Order.findOne({ bidId: bid._id }).select(
            "_id paymentStatus status"
          );
          if (order) {
            bidObj.order = order;
          }
        }
        return bidObj;
      })
    );

    res.status(200).json({
      success: true,
      count: totalCount,
      data: bidsWithOrderInfo,
      currentPage: page,
      totalPages: Math.ceil(totalCount / limit),
      itemsPerPage: limit
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller bids (bids on seller's content)
// @route   GET /api/bids/seller
// @access  Private/Seller/Admin
exports.getSellerBids = async (req, res, next) => {
  try {
    // Get pagination parameters from query
    const page = parseInt(req.query.page, 9) || 1;
    const limit = parseInt(req.query.limit, 9) || 9;
    const startIndex = (page - 1) * limit;

    // Find all content owned by the seller
    const Content = require("../models/Content");
    const sellerContent = await Content.find({ seller: req.user.id }).select(
      "_id"
    );
    const contentIds = sellerContent.map((content) => content._id);

    // Get total count for pagination
    const total = await Bid.countDocuments({ content: { $in: contentIds } });

    // Find all bids for seller's content with pagination
    const bids = await Bid.find({ content: { $in: contentIds } })
      .populate({
        path: "bidder",
        select: "firstName lastName email",
      })
      .populate({
        path: "content",
        select: "title sport contentType price thumbnailUrl auctionDetails",
      })
      .sort("-createdAt")
      .skip(startIndex)
      .limit(limit);

    res.status(200).json({
      success: true,
      count: bids.length,
      total,
      pagination: {
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      },
      data: bids,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller bids for specific content
// @route   GET /api/bids/seller/content/:contentId
// @access  Private/Seller
exports.getSellerContentBids = async (req, res, next) => {
  try {
    const { contentId } = req.params;

    // Verify the content exists and belongs to the seller
    const Content = require("../models/Content");
    const content = await Content.findById(contentId);

    if (!content) {
      return next(
        new ErrorResponse(`Content not found with id of ${contentId}`, 404)
      );
    }

    // Check if the seller owns this content
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view bids for this content`,
          403
        )
      );
    }

    // Find all bids for this specific content
    const bids = await Bid.find({ content: contentId })
      .populate({
        path: "bidder",
        select: "firstName lastName email",
      })
      .populate({
        path: "content",
        select: "title sport contentType price thumbnailUrl auctionDetails",
      })
      .sort("-amount"); // Sort by highest bid first

    res.status(200).json({
      success: true,
      count: bids.length,
      data: bids,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    End auction
// @route   PUT /api/bids/end-auction/:contentId
// @access  Private/Seller/Admin
exports.endAuction = async (req, res, next) => {
  try {
    const content = await Content.findById(req.params.contentId);

    if (!content) {
      return next(
        new ErrorResponse(
          `Content not found with id of ${req.params.contentId}`,
          404
        )
      );
    }

    // Make sure user is content seller or admin
    if (
      content.seller.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to end this auction`,
          403
        )
      );
    }

    // Check if content allows auction
    if (content.saleType !== "Auction" && content.saleType !== "Both") {
      return next(new ErrorResponse(`Content does not allow auction`, 400));
    }

    // Get highest bid
    const highestBid = await Bid.findOne({
      content: req.params.contentId,
      status: "Active",
    }).sort("-amount");

    // Check if there is a highest bid
    if (!highestBid) {
      return next(
        new ErrorResponse(`No active bids found for this content`, 400)
      );
    }

    // Check if bid meets reserve price
    if (
      content.auctionDetails.reservePrice &&
      highestBid.amount < content.auctionDetails.reservePrice
    ) {
      // Update all bids to Lost
      await Bid.updateMany(
        { content: req.params.contentId, status: "Active" },
        { status: "Lost" }
      );

      return res.status(200).json({
        success: true,
        message: "Auction ended but reserve price not met",
        data: null,
      });
    }

    // Cancel scheduled auction job since auction is being ended manually
    AuctionSchedulerService.cancelAuctionSchedule(content._id.toString());

    // Update content auction status and end time
    content.auctionStatus = "Ended";
    content.auctionEndedAt = new Date();
    content.auctionDetails.endTime = Date.now();
    await content.save();

    res.status(200).json({
      success: true,
      message: "Auction ended successfully. Please accept a bid to complete the sale.",
      data: {
        highestBid,
        message: "You must explicitly accept a bid to complete the sale"
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Accept or reject bid
// @route   PUT /api/bids/:id/status
// @access  Private/Seller
exports.updateBidStatus = async (req, res, next) => {
  try {
    const { status, sellerResponse } = req.body;

    if (!["accepted", "rejected"].includes(status)) {
      return next(
        new ErrorResponse("Status must be either accepted or rejected", 400)
      );
    }

    const bid = await Bid.findById(req.params.id).populate(
      "content",
      "seller title price saleType auctionDetails"
    );

    if (!bid) {
      return next(
        new ErrorResponse(`Bid not found with id of ${req.params.id}`, 404)
      );
    }

    // Check if user is the seller of the content
    if (
      req.user.role !== "admin" &&
      req.user.id !== bid.content.seller.toString()
    ) {
      return next(new ErrorResponse("Not authorized to update this bid", 403));
    }

    // Check if bid is still active
    if (bid.status !== "Active") {
      return next(new ErrorResponse("Bid is no longer active", 400));
    }

    if (status === "accepted") {
      // Update bid status to Won
      bid.status = "Won";
      await bid.save();

      // Create order for accepted bid using database settings
      const Order = require("../models/Order");
      const Setting = require("../models/Setting");
      const settings = await Setting.getSingleton();

      // Use the new fee calculation method for transparency
      const feeBreakdown = Setting.calculateFeeBreakdown(bid.amount, settings);
      const platformFee = feeBreakdown.platformCommission;
      const sellerEarnings = feeBreakdown.finalSellerEarnings; // Final amount after all fees
      const totalAmount = bid.amount; // Buyer pays exactly the listed price, not listed price + platform fee

      const { getPaymentDeadline } = require('../config/timeouts');

      const order = await Order.create({
        buyer: bid.bidder,
        seller: bid.content.seller,
        content: bid.content._id,
        orderType: "Auction",
        amount: bid.amount,
        platformFee: platformFee,
        sellerEarnings: sellerEarnings,
        totalAmount: totalAmount,
        bidId: bid._id,
        paymentDeadline: getPaymentDeadline(),
        status: "Pending",
      });

      // Update all other bids for this content to Lost
      await Bid.updateMany(
        { content: bid.content._id, status: "Active", _id: { $ne: bid._id } },
        { status: "Lost" }
      );

      // End the auction immediately and hide content from public listing
      const content = await Content.findById(bid.content._id).populate(
        "seller",
        "firstName lastName email"
      );

      if (content) {
        // Cancel scheduled auction job since bid is being accepted (auction ends)
        AuctionSchedulerService.cancelAuctionSchedule(content._id.toString());

        // Update auction status and end time
        content.auctionStatus = "Ended";
        content.auctionEndedAt = new Date();
        content.winningBidId = bid._id;

        // Update auction end time if it's an auction
        if (content.saleType === "Auction" || content.saleType === "Both") {
          content.auctionDetails.endTime = new Date();
        }

        await content.save();
      }

      // Get bidder information for email
      const User = require("../models/User");
      const bidderInfo = await User.findById(bid.bidder).select(
        "firstName lastName email"
      );

      // Send email notification to winning bidder in background (don't await)
      if (bidderInfo && content) {
        const checkoutUrl = `${process.env.FRONTEND_URL}/checkout/${order._id}`;

        const emailData = generateBidAcceptanceEmail({
          bidder: bidderInfo,
          content: {
            title: content.title,
            sport: content.sport,
            contentType: content.contentType,
            seller: content.seller,
          },
          bid: bid,
          checkoutUrl: checkoutUrl,
        });

        sendEmail({
          email: bidderInfo.email,
          subject: emailData.subject,
          message: emailData.message,
          html: emailData.html,
        }).then(() => {
          console.log(`Bid acceptance email sent to ${bidderInfo.email}`);
        }).catch((emailError) => {
          console.error("Error sending bid acceptance email:", emailError);
          // Email failure doesn't affect bid acceptance response
        });
      }

      res.status(200).json({
        success: true,
        message: "Bid accepted successfully. Auction ended and buyer notified.",
        data: {
          bid,
          orderId: order._id,
          auctionEnded: true,
        },
      });
    } else if (status === "rejected") {
      // Update bid status to Lost
      bid.status = "Lost";
      await bid.save();

      // Get bidder information for rejection email
      const User = require("../models/User");
      const bidderInfo = await User.findById(bid.bidder).select(
        "firstName lastName email"
      );

      // Get content information for email
      const content = await Content.findById(bid.content._id).populate(
        "seller",
        "firstName lastName email"
      );

      // Send email notification to rejected bidder
      if (bidderInfo && content) {
        try {
          const emailData = generateBidRejectionEmail({
            bidder: bidderInfo,
            content: {
              title: content.title,
              sport: content.sport,
              contentType: content.contentType,
              seller: content.seller,
            },
            bid: bid,
            sellerResponse: sellerResponse || 'The seller has decided not to accept your bid at this time.',
          });

          await sendEmail({
            email: bidderInfo.email,
            subject: emailData.subject,
            message: emailData.message,
            html: emailData.html,
          });

          console.log(`Bid rejection email sent to ${bidderInfo.email}`);
        } catch (emailError) {
          console.error("Error sending bid rejection email:", emailError);
          // Don't fail the bid rejection if email fails
        }
      }

      res.status(200).json({
        success: true,
        message: "Bid rejected successfully",
        data: bid,
      });
    }
  } catch (err) {
    next(err);
  }
};
