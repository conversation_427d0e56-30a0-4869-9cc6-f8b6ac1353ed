const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const AuctionSchedulerService = require('../services/auctionSchedulerService');

// @desc    Get scheduled auctions status
// @route   GET /api/auction-scheduler/status
// @access  Private/Admin
router.get('/status', protect, authorize('admin'), (req, res) => {
    try {
        const status = AuctionSchedulerService.getScheduledAuctionsStatus();
        
        res.status(200).json({
            success: true,
            data: status
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Cleanup expired auction jobs
// @route   POST /api/auction-scheduler/cleanup
// @access  Private/Admin
router.post('/cleanup', protect, authorize('admin'), (req, res) => {
    try {
        const cleanedCount = AuctionSchedulerService.cleanupExpiredJobs();
        
        res.status(200).json({
            success: true,
            data: {
                cleanedJobs: cleanedCount
            },
            message: `Cleaned up ${cleanedCount} expired auction jobs`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Reschedule all existing auctions
// @route   POST /api/auction-scheduler/reschedule
// @access  Private/Admin
router.post('/reschedule', protect, authorize('admin'), async (req, res) => {
    try {
        const scheduledCount = await AuctionSchedulerService.scheduleExistingAuctions();
        
        res.status(200).json({
            success: true,
            data: {
                scheduledAuctions: scheduledCount
            },
            message: `Scheduled ${scheduledCount} auctions`
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
